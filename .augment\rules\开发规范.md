---
type: "always_apply"
---

# 房联移动端前端项目 - AI 开发规范文档

## 1. 项目概述

### 1.1 项目简介
房联（funi）移动端前端项目是一个基于 Vue 3 + Vite + Vant 的移动端前端应用，专为房联业务场景设计。项目采用现代化的前端技术栈，提供高性能的移动端用户体验。

### 1.2 核心技术栈
- **前端框架**: Vue 3.5.17 (Composition API)
- **构建工具**: Vite 7.0.3
- **UI 组件库**: Vant 4.9.20 (移动端专用 UI 库)
- **路由管理**: Vue Router 4.5.1 + unplugin-vue-router (文件系统路由)
- **状态管理**: Pinia 3.0.3 + pinia-plugin-persistedstate (持久化支持)
- **样式处理**: SCSS + PostCSS (移动端适配)
- **HTTP 请求**: @funi-lib/utils (房联自研工具库)
- **包管理器**: pnpm
- **开发服务器**: 默认端口 8000

### 1.3 项目特色
- 🚀 基于 Vite 的快速开发体验
- 📱 专为移动端优化的响应式设计 (375px 基准，最大 600px)
- 🎨 完整的 Vant UI 组件库集成
- 🗂️ 文件系统路由，开发更直观
- 💾 状态持久化，用户体验更佳
- 🔧 自动组件注册，减少样板代码
- 🔐 内置加密和认证机制

## 2. 代码结构和组织

### 2.1 目录结构
详情见文档：[项目结构](docs/项目结构.md)


### 2.2 命名约定
- **页面文件**: kebab-case，如 `user-profile.vue`
- **组件文件**: PascalCase，如 `UserCard.vue`
- **工具文件**: camelCase，如 `formatDate.js`
- **常量文件**: UPPER_SNAKE_CASE，如 `API_CONSTANTS.js`
- **目录名**: kebab-case，如 `user-management/`

### 2.3 模块组织原则
- 按功能模块组织页面目录
- 页面私有组件放在对应页面的 `components/` 目录
- 页面共用逻辑放在对应页面的 `common/` 目录
- HTML 模板必须放在 `html/` 目录下

## 3. 开发标准

### 3.1 页面组件开发规范

#### 3.1.1 基本结构
每个页面组件必须遵循以下结构：

```vue
<!-- 页面html模板 -->
<template src='./html/index.html'></template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'

// 响应式数据
const loading = ref(false)
const dataList = ref([])

// 计算属性
const pageTitle = computed(() => '页面标题')

// 方法
const fetchData = async () => {
  loading.value = true
  try {
    // API 调用
  } finally {
    loading.value = false
  }
}

// 生命周期
onMounted(() => {
  fetchData()
})
</script>

<style lang="scss" scoped>
.page-container {
  min-height: 100vh;
  background-color: var(--van-background-2);
}

.content {
  padding: 16px;
}
</style>

<route lang="json5">
{
  name: 'PageName',
  meta: {
    title: '页面标题'
  }
}
</route>
```

#### 3.1.2 必需元素
1. **HTML 模板**: 必须使用外部 HTML 文件，路径为 `./html/index.html`
2. **路由块**: 必须包含 `<route>` 块定义路由信息
3. **样式作用域**: 必须添加 `scoped` 属性
4. **导航栏**: 无需单独添加，App.vue 已包含全局导航栏

### 3.2 代码风格规范

#### 3.2.1 Vue 组件规范
- 使用 Vue 3 Composition API
- 优先使用 `<script setup>` 语法
- 响应式数据使用 `ref()` 或 `reactive()`
- 计算属性使用 `computed()`
- 生命周期钩子按需导入

#### 3.2.2 变量和函数命名
- 响应式数据: camelCase，如 `userInfo`, `isLoading`
- 函数名: camelCase，动词开头，如 `fetchData`, `handleClick`
- 常量: UPPER_SNAKE_CASE，如 `API_BASE_URL`
- 组件 props: camelCase，如 `userName`, `isVisible`

#### 3.2.3 注释和文档标准
- API 函数必须包含 JSDoc 注释
- 复杂业务逻辑添加行内注释
- 组件 props 和 emits 添加类型注释

```javascript
/**
 * 获取待开会议列表
 * @param {Object} params - 查询参数
 * @param {number} params.current - 当前页码
 * @param {number} params.size - 每页条数
 * @param {string} params.assignee - 当前用户账号
 * @param {string} [params.conferenceName] - 会议名称（可选）
 * @returns {Promise} 返回会议列表数据
 */
```

#### 3.2.4 导入/导出模式
- 使用 ES6 模块语法
- 按类型分组导入：Vue API、第三方库、本地模块
- 使用命名导出而非默认导出（除组件外）

```javascript
// Vue API
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'

// 第三方库
import { showToast } from 'vant'

// 本地模块
import { meetingApi } from '@/api/meeting'
```

## 4. UI/UX 指南

### 4.1 组件库使用
- **优先使用**: Vant UI 组件库（已全局注册）
- **组件前缀**: 所有 Vant 组件使用 `Van` 前缀，如 `VanButton`, `VanCell`
- **避免**: Element Plus 或其他 PC 端组件库

### 4.2 样式开发规范

#### 4.2.1 SCSS 使用规范
```scss
.page-container {
  // 使用 BEM 命名法
  &__header {
    background: var(--van-primary-color);
    
    .title {
      font-size: 18px;
      color: white;
    }
  }
  
  &__content {
    padding: 16px;
    
    // 响应式断点
    @media (max-width: 320px) {
      padding: 12px;
    }
  }
}
```

#### 4.2.2 CSS 变量使用
- 优先使用 Vant 提供的 CSS 变量
- 颜色: `var(--van-primary-color)`, `var(--van-text-color)`
- 间距: `var(--van-padding-md)`, `var(--van-padding-lg)`
- 字体: `var(--van-font-size-md)`, `var(--van-font-size-lg)`

### 4.3 响应式设计模式
- **设计基准**: 375px 宽度
- **最大显示宽度**: 600px
- **单位使用**: 直接使用 px，postcss-mobile-forever 自动转换
- **安全区域**: 使用 `env(safe-area-inset-bottom)` 适配

### 4.4 主题定制
在 `src/style/theme.css` 中修改 Vant 主题变量：

```css
:root {
  --van-primary-color: #1989fa;
  --van-success-color: #07c160;
  --van-warning-color: #ff976a;
  --van-danger-color: #ee0a24;
}
```

## 5. 构建和配置

### 5.1 包管理
- **包管理器**: pnpm (必须使用)
- **依赖安装**: `pnpm install`
- **开发启动**: `pnpm dev`
- **生产构建**: `pnpm build`

### 5.2 构建配置
- **基础路径**: 相对路径 `./`
- **开发端口**: 8000
- **代理配置**: `/api` 路径代理到后端
- **别名配置**: `@` -> `src/`, `~` -> `src/assets/`

### 5.3 环境配置
- **Node.js**: >= 20.0.0
- **pnpm**: >= 7.0.0
- **开发工具**: 推荐 VS Code + Volar 扩展

## 6. 核心功能使用指南

### 6.1 文件系统路由

#### 6.1.1 路由映射规则
- `src/pages/home/<USER>/home`
- `src/pages/user/profile.vue` → `/user/profile`
- `src/pages/meeting/[id].vue` → `/meeting/:id` (动态路由)

#### 6.1.2 路由配置
```vue
<route lang="json5">
{
  name: 'MeetingDetail',
  meta: {
    title: '会议详情',
    requiresAuth: true,
    keepAlive: false
  }
}
</route>
```

#### 6.1.3 排除规则
- `components/` 文件夹自动排除
- `common/` 文件夹自动排除
- `html/` 文件夹自动排除

### 6.2 状态管理 (Pinia)

#### 6.2.1 Store 创建模式
```javascript
// src/stores/modules/user.js
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useUserStore = defineStore('user', () => {
  // 状态
  const userInfo = ref(null)
  const token = ref('')

  // 计算属性
  const isLoggedIn = computed(() => !!token.value)

  // 方法
  const login = async (credentials) => {
    const response = await window.$http.post('/api/login', credentials)
    token.value = response.token
    userInfo.value = response.user
  }

  return {
    userInfo,
    token,
    isLoggedIn,
    login
  }
}, {
  persist: true // 启用持久化
})
```

#### 6.2.2 Store 使用模式
```vue
<script setup>
import { useUserStore } from '@/stores/modules/user'

const userStore = useUserStore()

// 访问状态
console.log(userStore.userInfo)

// 调用方法
const handleLogin = () => {
  userStore.login({ username: 'admin', password: '123456' })
}
</script>
```

### 6.3 HTTP 请求规范

#### 6.3.1 全局 HTTP 实例
项目使用全局 `window.$http` 实例，基于 @funi-lib/utils：

```javascript
// GET 请求
const response = await window.$http.fetch('/api/users', { page: 1 })

// POST 请求
const response = await window.$http.post('/api/users', userData)

// PUT 请求
const response = await window.$http.put('/api/users/1', userData)

// DELETE 请求
const response = await window.$http.delete('/api/users/1')
```

#### 6.3.2 API 模块化组织
```javascript
// src/api/meeting.js
export const meetingApi = {
  /**
   * 获取待开会议列表
   * @param {Object} params - 查询参数
   * @returns {Promise} 返回会议列表数据
   */
  getKeeperFromDataApp: (params) => {
    return window.$http.fetch('/conference/keeperFromDataApp', params, {
      'keeper-auth': '{{keeper-auth}}'
    })
  }
}
```

#### 6.3.3 错误处理
- HTTP 实例已配置统一错误处理
- 自动处理认证失败、网络错误等
- 特殊错误可在业务代码中自定义处理

### 6.4 组件开发规范

#### 6.4.1 公共组件
- 放在 `src/components/` 目录
- 使用 PascalCase 命名
- 自动全局注册（通过 unplugin-vue-components）

#### 6.4.2 页面私有组件
- 放在对应页面的 `components/` 目录
- 不会被路由系统识别
- 仅在当前页面模块内使用

## 7. 移动端适配详细说明

### 7.1 PostCSS 配置
```javascript
// postcss.config.js
export default {
  plugins: {
    'postcss-mobile-forever': {
      appSelector: '#app',
      viewportWidth: 375,      // 设计稿基准宽度
      maxDisplayWidth: 600,    // 最大显示宽度
      border: true,            // 自动添加边框
      rootContainingBlockSelectorList: [
        '.van-tabbar',
        '.van-popup',
        // 需要转换的 fixed 选择器
      ]
    }
  }
}
```

### 7.2 样式适配原则
- 直接使用 px 单位，插件自动转换
- 设计稿按 375px 宽度设计
- 最大显示宽度限制为 600px
- 自动处理安全区域适配


## 8. 开发注意事项和最佳实践

### 8.1 强制性规范
1. **组件库优先级**: 必须优先使用 Vant UI 组件
2. **HTML 模板**: 必须使用外部 HTML 文件，放在 `html/` 目录
3. **路由配置**: 每个页面必须包含 `<route>` 块
4. **样式作用域**: 页面样式必须添加 `scoped` 属性
5. **项目结构更新**: 文件结构变更后必须更新 `docs/项目结构.md`

### 8.2 性能优化建议
- 使用 `keep-alive` 缓存重要页面
- 图片资源使用适当格式和尺寸
- 避免在模板中使用复杂计算
- 合理使用 Pinia 持久化功能

### 8.3 安全注意事项
- HTTP 请求自动处理加密和认证
- 敏感信息不要存储在前端
- 使用 sessionStorage 存储临时认证信息

### 8.4 调试和开发工具
- 使用 Vue DevTools 调试组件状态
- 使用浏览器开发者工具模拟移动设备
- 利用 Vite 的热重载功能快速开发

**重要提醒**:
- 此文档是 AI 开发助手的核心参考，所有代码变更必须严格遵循
- 开发前务必熟悉项目结构和技术栈
- 遇到问题时优先查阅此文档和现有代码示例
- 保持代码风格一致性，提高项目可维护性
